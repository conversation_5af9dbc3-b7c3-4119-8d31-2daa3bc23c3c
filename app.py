import streamlit as st
import pandas as pd
import re
from pathlib import Path
import io
import zipfile
from dose_merge_generator import DoseMergeGenerator
from td_merge_generator import TDMergeGenerator
from subject_info_generator import SubjectInfoGenerator
from patient_profile_generator import PatientProfileGenerator
from ae_writeout_generator import AEWriteOutGenerator
from controller_generator import ControllerGenerator
import openpyxl
from openpyxl import Workbook
import numpy as np
import warnings

# Suppress openpyxl Data Validation warning
warnings.filterwarnings('ignore', category=UserWarning, module='openpyxl')

def load_r_file(uploaded_file):
    """Load R file content from uploaded file."""
    return uploaded_file.getvalue().decode('utf-8')

def save_r_file(file_path, content):
    """Save modified R file content."""
    with open(file_path, 'w') as f:
        f.write(content)

def confirm_changes(file_name, edited_code):
    """Confirm changes made to a file."""
    st.session_state.confirmed_files[file_name] = edited_code
    st.session_state.confirmation_status[file_name] = True

def load_config_file():
    """Load and validate the configuration file."""
    try:
        config_df = pd.read_excel("//10.8.52.10/Spotfire/MDR_Auto/config_var_replace/Config_MDR_var_replacement.xlsx", engine='openpyxl')
        required_columns = ['Var_to_Replace', 'FieldPattern', 'FormPattern']

        # Check if required columns exist
        missing_cols = [col for col in required_columns if col not in config_df.columns]
        if missing_cols:
            raise ValueError(f"Missing required columns in config file: {', '.join(missing_cols)}")

        return config_df
    except Exception as e:
        raise Exception(f"Error loading config file: {str(e)}")

def find_matching_variables(var_label_value, config_df):
    """
    Find matching variables based on config patterns and var_label_value data.
    Returns a dictionary mapping old variables to new variables.
    """
    if 'FieldOID' not in var_label_value.columns or 'DraftFormName' not in var_label_value.columns or 'SASLabel' not in var_label_value.columns:
        raise ValueError("var_label_value must contain 'FieldOID', 'SASLabel', and 'DraftFormName' columns")

    variable_mappings = {}
    replacements_info = []

    for _, config_row in config_df.iterrows():
        old_var = config_row['Var_to_Replace']
        field_pattern = config_row['FieldPattern']
        form_pattern = config_row['FormPattern']

        # Filter data based on form pattern first
        matching_forms = var_label_value[var_label_value['DraftFormName'].str.contains(form_pattern, regex=True, na=False)]

        if not matching_forms.empty:
            # Then find matching field pattern within the filtered forms using SASLabel
            matching_vars = matching_forms[
                matching_forms['SASLabel'].str.contains(field_pattern, regex=True, na=False)
            ]

            if not matching_vars.empty:
                # Use the FieldOID as the new variable name
                new_var = matching_vars.iloc[0]['FieldOID']
                variable_mappings[old_var] = new_var
                replacements_info.append({
                    'old_var': old_var,
                    'new_var': new_var,
                    'form': matching_vars.iloc[0]['DraftFormName'],
                    'pattern_used': field_pattern,
                    'matched_saslabel': matching_vars.iloc[0]['SASLabel']  # Added for verification
                })

    return variable_mappings, replacements_info

def replace_variables(r_code, variable_mappings):
    """Replace variables in R code based on mapping dictionary."""
    modified_code = r_code
    replacements_made = []

    for old_var, new_var in variable_mappings.items():
        if pd.notna(old_var) and pd.notna(new_var):
            # Convert to string and strip whitespace
            old_var = str(old_var).strip()
            new_var = str(new_var).strip()

            if old_var and new_var:  # Check if non-empty after stripping
                # Create patterns for base variable and variables with _PT and _PROD suffixes
                base_pattern = r'(?<=[^\w])' + re.escape(old_var) + r'(?=[^\w])'
                pt_pattern = r'(?<=[^\w])' + re.escape(old_var + '_PT') + r'(?=[^\w])'
                soc_pattern = r'(?<=[^\w])' + re.escape(old_var + '_SOC') + r'(?=[^\w])'
                prod_pattern = r'(?<=[^\w])' + re.escape(old_var + '_PROD') + r'(?=[^\w])'
                Int_pattern = r'(?<=[^\w])' + re.escape(old_var + '_INT') + r'(?=[^\w])'

                # Replace base variable
                new_code = re.sub(base_pattern, new_var, modified_code)

                # Replace variables with _PT suffix
                if re.search(pt_pattern, modified_code):
                    new_code = re.sub(pt_pattern, new_var + '_PT', new_code)
                    if new_code != modified_code:
                        replacements_made.append(f"'{old_var}_PT' → '{new_var}_PT'")

                # Replace variables with _PROD suffix
                if re.search(prod_pattern, modified_code):
                    new_code = re.sub(prod_pattern, new_var + '_PROD', new_code)
                    if new_code != modified_code:
                        replacements_made.append(f"'{old_var}_PROD' → '{new_var}_PROD'")

                # Replace variables with _SOC suffix
                if re.search(soc_pattern, modified_code):
                    new_code = re.sub(soc_pattern, new_var + '_SOC', new_code)
                    if new_code != modified_code:
                        replacements_made.append(f"'{old_var}_SOC' → '{new_var}_SOC'")

                # Replace variables with _INT suffix
                if re.search(Int_pattern, modified_code):
                    new_code = re.sub(Int_pattern, new_var + '_INT', new_code)
                    if new_code != modified_code:
                        replacements_made.append(f"'{old_var}_INT' → '{new_var}_INT'")

                # Check if any base variable replacements were made
                if new_code != modified_code:
                    replacements_made.append(f"'{old_var}' → '{new_var}'")
                    modified_code = new_code

    return modified_code, replacements_made

def create_zip_of_modified_files(modified_files):
    """Create a zip file containing all modified files."""
    zip_buffer = io.BytesIO()
    with zipfile.ZipFile(zip_buffer, "w", zipfile.ZIP_DEFLATED) as zip_file:
        for filename, content in modified_files.items():
            # Handle both text and binary content
            if isinstance(content, bytes):
                zip_file.writestr(filename, content)
            else:
                zip_file.writestr(filename, content.encode('utf-8') if isinstance(content, str) else content)
    return zip_buffer.getvalue()

def generate_config_combo(als_file, blind, lesion_metric, anchor_type, study_unit):
    """Generate config combo files from ALS file."""
    try:
        # Save the uploaded file temporarily
        with open("temp_als_file.xlsx", "wb") as f:
            f.write(als_file.getvalue())

        # Read the ALS file sheets with explicit engine
        als_field = pd.read_excel("temp_als_file.xlsx", sheet_name="Fields", engine='openpyxl')
        als_field = als_field[~als_field['DraftFieldName'].str.contains('_INACTIVE', na=False)]
        als_dataDic = pd.read_excel("temp_als_file.xlsx", sheet_name="DataDictionaryEntries", engine='openpyxl')
        als_form = pd.read_excel("temp_als_file.xlsx", sheet_name="Forms", engine='openpyxl')
        als_form = als_form[~als_form['DraftFormName'].str.contains('_INACTIVE', na=False)]
        als_project = pd.read_excel("temp_als_file.xlsx", sheet_name="CRFDraft", engine='openpyxl')
        als_folders = pd.read_excel("temp_als_file.xlsx", sheet_name="Folders", engine='openpyxl')

        # Clean up temporary file
        Path("temp_als_file.xlsx").unlink()

        # Get study name
        study_name = re.sub(r'[^a-zA-Z0-9_]', '_', f"{blind}_{als_project['ProjectName'].iloc[0].lower().replace('-', '_')}")

        # Process variable label table
        variable_label_tab = als_form[['OID', 'DraftFormName']].copy()
        variable_label_tab = variable_label_tab[variable_label_tab['OID'].notna()]
        variable_label_tab = variable_label_tab.merge(
            als_field[['FormOID', 'FieldOID', 'SASLabel', 'DataDictionaryName']],
            left_on='OID',
            right_on='FormOID'
        ).drop('FormOID', axis=1)
        variable_label_tab['OID'] = variable_label_tab['OID'].str.lower()

        # Process variable value table
        variable_value_tab = als_dataDic[['DataDictionaryName', 'UserDataString']].copy()
        variable_value_tab = variable_value_tab[variable_value_tab['DataDictionaryName'].notna()]

        # Map variable name, label and value
        var_label_value = variable_value_tab.merge(
            variable_label_tab,
            on='DataDictionaryName',
            how='outer'
        ).sort_values(['OID', 'FieldOID']).drop('DataDictionaryName', axis=1)

        # Store var_label_value in session state for variable replacement functionality
        st.session_state.var_label_value = var_label_value

        # Process DA_ALS
        da_als = var_label_value.copy()
        da_als['study_name'] = study_name
        da_als = da_als[da_als['DraftFormName'].str.contains('RECIST|Enrollment', case=False, na=False)].reset_index(drop=True)

        # Add r_name based on DraftFormName
        conditions = [
            da_als['DraftFormName'].str.contains('Time-point Response Assessment', case=False, na=False),
            da_als['DraftFormName'].str.contains('Non-Target Lesions', case=False, na=False),
            da_als['DraftFormName'].str.contains('Target Lesions', case=False, na=False),
            da_als['DraftFormName'].str.contains('New Lesions', case=False, na=False),
            da_als['DraftFormName'].str.contains('Enrollment', case=False, na=False)
        ]
        choices = ['rs_tb', 'ntl_tb', 'tl_tb', 'nl_tb', 'enr_tb']
        da_als['r_name'] = np.select(conditions, choices, default=None)
        da_als = da_als[da_als['r_name'].notna()].reset_index(drop=True)

        # Add r_col_name based on SASLabel
        conditions = [
            (da_als['SASLabel'].str.contains('Date', case=False, na=False)) & (da_als['r_name'] == 'rs_tb'),
            (da_als['SASLabel'].str.contains('^Overall response', case=False, na=False)) & (da_als['r_name'] == 'rs_tb'),
            (da_als['SASLabel'].str.contains('Evaluation of target lesions', case=False, na=False)) & (da_als['r_name'] == 'rs_tb'),
            (da_als['SASLabel'].str.contains('Evaluation of non-target lesions', case=False, na=False)) & (da_als['r_name'] == 'rs_tb'),
            (da_als['SASLabel'].str.contains('New lesion', case=False, na=False)) & (da_als['r_name'] == 'rs_tb'),
            (da_als['SASLabel'].str.contains('Sum|Max', case=False, na=False)) & (da_als['r_name'] == 'tl_tb'),
            (da_als['SASLabel'].str.contains('Evaluated: Diameter measurement', case=False, na=False)) & (da_als['r_name'] == 'tl_tb'),
            (da_als['SASLabel'].str.contains('Diameter lesion', case=False, na=False)) & (da_als['r_name'] == 'tl_tb'),
            (da_als['SASLabel'].str.contains('Coalesced, lesion diameter', case=False, na=False)) & (da_als['r_name'] == 'tl_tb'),
            (da_als['SASLabel'].str.contains('Too small to measure', case=False, na=False)) & (da_als['r_name'] == 'tl_tb'),
            (da_als['SASLabel'].str.contains('Date of procedure', case=False, na=False)) & (da_als['r_name'] == 'tl_tb'),
            (da_als['SASLabel'].str.contains('Location', case=False, na=False)) & (da_als['r_name'] == 'tl_tb'),
            (da_als['SASLabel'].str.contains('Lesion status', case=False, na=False)) & (da_als['r_name'] == 'tl_tb'),
            (da_als['SASLabel'].str.contains('Target lesion number', case=False, na=False)) & (da_als['r_name'] == 'tl_tb'),
            (da_als['SASLabel'].str.contains('Lesion status', case=False, na=False)) & (da_als['r_name'] == 'ntl_tb'),
            (da_als['SASLabel'].str.contains('Date of procedure', case=False, na=False)) & (da_als['r_name'] == 'ntl_tb'),
            (da_als['SASLabel'].str.contains('Location', case=False, na=False)) & (da_als['r_name'] == 'ntl_tb'),
            (da_als['SASLabel'].str.contains('Were there new lesions to report', case=False, na=False)) & (da_als['r_name'] == 'nl_tb'),
            (da_als['SASLabel'].str.contains('Date of enrollment', case=False, na=False)) & (da_als['r_name'] == 'enr_tb')
        ]
        choices = [
            'rs_date', 'rs_overall_response', 'rs_tl_response', 'rs_ntl_response', 'rs_nl_response',
            'tl_diam_metric', 'tl_diam', 'tl_split_diam', 'tl_coalesce_diam', 'tl_too_small_diam',
            'tl_date', 'tl_location', 'tl_status', 'tl_id', 'ntl_response', 'ntl_date', 'ntl_location',
            'nl_yn', 'enr_date'
        ]
        da_als['r_col_name'] = np.select(conditions, choices, default=None)
        da_als = da_als[da_als['r_col_name'].notna()].reset_index(drop=True)

        # Add crf_col_type based on r_col_name
        da_als['crf_col_type'] = np.where(
            da_als['r_col_name'].str.contains('^tl(_.*_diam|_diam)|tl_id|tl_diam_metric', case=False, na=False),
            'numeric',
            np.where(
                da_als['SASLabel'].str.contains('Date', case=False, na=False),
                'date',
                'character'
            )
        )

        # Add r_col_val_name based on UserDataString
        conditions = [
            da_als['UserDataString'].str.contains('Absent', case=False, na=False),
            da_als['UserDataString'].str.contains('Coalesced', case=False, na=False),
            da_als['UserDataString'].str.contains('Complete', case=False, na=False),
            da_als['UserDataString'].str.contains('^Evaluated', case=False, na=False),
            da_als['UserDataString'].str.contains('No longer visible', case=False, na=False),
            da_als['UserDataString'].str.contains('Lymph', case=False, na=False),
            da_als['UserDataString'].str.contains('Not Applicable', case=False, na=False),
            da_als['UserDataString'].str.contains('Not.*Evalua|not done', case=False, na=False),
            (da_als['UserDataString'].str.contains('No', case=False, na=False)) & (da_als['r_col_name'].isin(['rs_nl_response', 'nl_yn'])),
            da_als['UserDataString'].str.contains('Yes', case=False, na=False),
            da_als['UserDataString'].str.contains('Partial', case=False, na=False),
            da_als['UserDataString'].str.contains('Stable', case=False, na=False),
            da_als['UserDataString'].str.contains('Non-CR', case=False, na=False),
            da_als['UserDataString'].str.contains('Unequivocal Progression', case=False, na=False),
            da_als['UserDataString'].str.contains('Progress', case=False, na=False),
            da_als['UserDataString'].str.contains('Present', case=False, na=False),
            da_als['UserDataString'].str.contains('Split', case=False, na=False),
            da_als['UserDataString'].str.contains('Unknown', case=False, na=False),
            da_als['UserDataString'].str.contains('Too small to measure', case=False, na=False)
        ]
        choices = [
            'Absent', 'cl', 'CR', 'el', 'invisible', 'lymph', 'NA', 'NE', 'No', 'Yes',
            'PR', 'SD', 'Non-CR/Non-PD', 'Unequivocal Progression', 'PD', 'Present',
            'sp', 'un', 'tstm'
        ]
        da_als['r_col_val_name'] = np.select(conditions, choices, default=None)

        # Add Flag_for_lymph and filter
        da_als['Flag_for_lymph'] = np.where(
            (da_als['SASLabel'].str.contains('Location', case=False, na=False)) &
            (~da_als['UserDataString'].str.contains('Lymph', case=False, na=False)),
            1, 0
        )
        da_als = da_als[da_als['Flag_for_lymph'] != 1].reset_index(drop=True)

        # Rename columns
        da_als = da_als.rename(columns={
            'UserDataString': 'crf_col_val_name',
            'OID': 'crf_name',
            'FieldOID': 'crf_col_name',
            'SASLabel': 'crf_col_desc'
        })

        # Modify crf_col_name for date fields
        da_als['crf_col_name'] = np.where(
            (da_als['crf_col_desc'].str.contains('Date', case=False, na=False)) &
            (da_als['r_name'] != 'subject_info'),
            da_als['crf_col_name'] + '_INT',
            da_als['crf_col_name']
        )

        # Select and arrange columns
        da_als = da_als[[
            'study_name', 'r_name', 'crf_name', 'r_col_name', 'crf_col_name',
            'crf_col_type', 'r_col_val_name', 'crf_col_val_name', 'crf_col_desc'
        ]].reset_index(drop=True)

        # Add system variables
        config_systemVar = ['subj_id', 'instance_id', 'instance_name']
        crf_col = ['Subject', 'instanceId', 'InstanceName']
        type_systemVar = ['character', 'numeric', 'character']
        col_des = [
            'Unique subject identifier',
            'Unique folder instance identifier',
            'Name of the folder instance'
        ]

        system_rows = []
        for r_name in da_als['r_name'].unique():
            for var, col, type_var, desc in zip(config_systemVar, crf_col, type_systemVar, col_des):
                system_rows.append({
                    'study_name': study_name,
                    'r_name': r_name,
                    'crf_name': da_als[da_als['r_name'] == r_name]['crf_name'].iloc[0],
                    'r_col_name': var,
                    'crf_col_name': col,
                    'crf_col_type': type_var,
                    'r_col_val_name': None,
                    'crf_col_val_name': None,
                    'crf_col_desc': desc
                })

        # Add subject info table
        active_treatment = re.split(r'-\d+$', als_project['ProjectName'].iloc[0])[0]
        subject_info_rows = pd.DataFrame({
            'study_name': study_name,
            'r_name': 'subject_info',
            'crf_name': 'SubjectInfo',
            'r_col_name': [
                'subj_id', 'first_dose_date', 'last_dose_date', 'eos_date',
                'eot_date', 'eot_reason', 'death_date', 'eos_reason', 'death_cause'
            ],
            'crf_col_name': [
                'Subject name or identifier', 'FirstDoseDate', 'LastDoseDate',
                'EOS Date', f'EOT Date -{active_treatment}',
                f'EOT Reason -{active_treatment}', 'Death Date', 'EOS Reason',
                'Death Cause'
            ],
            'crf_col_desc': [
                'Unique subject identifier', 'Calculated first dose date',
                'Calculated last dose date', 'End of Study Date',
                'End of Treatment Date', 'End of Treatment Reason',
                'Death date', 'End of Study Reason', 'cause of death'
            ],
            'crf_col_val_name': [None] * 9,
            'r_col_val_name': [None] * 9
        })
        subject_info_rows['crf_col_type'] = np.where(
            subject_info_rows['crf_col_name'].str.contains('Date', case=False, na=False),
            'date',
            'character'
        )

        # Combine all tables
        da_crf_config = pd.concat([
            da_als,
            pd.DataFrame(system_rows),
            subject_info_rows
        ], ignore_index=True)

        # Filter out enrollment table system variables
        da_crf_config = da_crf_config[
            ~((da_crf_config['r_name'] == 'enr_tb') &
              (da_crf_config['r_col_name'].isin(['instance_id', 'instance_name'])))
        ].reset_index(drop=True)

        # Sort by r_name
        da_crf_config = da_crf_config.sort_values(by=['r_name', 'r_col_name']).reset_index(drop=True)

        # Process vs_crf_config
        vs_crf_config_ALS = var_label_value.copy()
        vs_crf_config_ALS = vs_crf_config_ALS[
            vs_crf_config_ALS['DraftFormName'].str.contains('Vital Sign.*|ECG|Weight', case=False, na=False)
        ]
        vs_crf_config_ALS = vs_crf_config_ALS[
            ~vs_crf_config_ALS['DraftFormName'].str.contains('NotinUse|Central', case=False, na=False)
        ]
        vs_crf_config_ALS['study_name'] = study_name

        # Add r_col_name for vs_crf_config
        conditions = [
            vs_crf_config_ALS['SASLabel'].str.contains('Were vital signs taken?', case=False, na=False),
            vs_crf_config_ALS['SASLabel'].str.contains('Date', case=False, na=False),
            vs_crf_config_ALS['SASLabel'].str.contains('Temperature', case=False, na=False),
            vs_crf_config_ALS['SASLabel'].str.contains('weight', case=False, na=False),
            vs_crf_config_ALS['SASLabel'].str.contains('Systolic blood pressure', case=False, na=False),
            vs_crf_config_ALS['SASLabel'].str.contains('Diastolic blood pressure', case=False, na=False),
            (vs_crf_config_ALS['SASLabel'].str.contains('Time point', case=False, na=False)) &
            (vs_crf_config_ALS['DraftFormName'].str.contains('Vital Sign', case=False, na=False)),
            vs_crf_config_ALS['SASLabel'].str.contains('Was an ECG performed?', case=False, na=False),
            vs_crf_config_ALS['SASLabel'].str.contains('QTcF', case=False, na=False),
            vs_crf_config_ALS['SASLabel'].str.contains('Was weight.* taken?', case=False, na=False)
        ]
        choices = [
            'VSYN', 'TESTDAT_INT', 'temp_stdvalue', 'weight_stdvalue',
            'SystolicBP_stdvalue', 'DiastolicBP_stdvalue', 'Timepoint',
            'EGPERF', 'stdvalue', 'WEYN'
        ]
        vs_crf_config_ALS['r_col_name'] = np.select(conditions, choices, default=None)
        vs_crf_config_ALS = vs_crf_config_ALS[vs_crf_config_ALS['r_col_name'].notna()]
        vs_crf_config_ALS = vs_crf_config_ALS[~vs_crf_config_ALS['DraftFormName'].str.contains('Weight', case=False, na=False)]
        vs_crf_config_ALS = vs_crf_config_ALS[~vs_crf_config_ALS['SASLabel'].str.contains('Date of weight', case=False, na=False)]
        vs_crf_config_ALS = vs_crf_config_ALS[~vs_crf_config_ALS['FieldOID'].str.contains('RSG', case=False, na=False)]
        vs_crf_config_ALS = vs_crf_config_ALS.drop('DraftFormName', axis=1)

        # Rename columns
        vs_crf_config_ALS = vs_crf_config_ALS.rename(columns={
            'OID': 'crf_name',
            'FieldOID': 'crf_col_name',
            'SASLabel': 'crf_col_desc',
            'UserDataString': 'crf_col_val_name'
        })

        # Update crf_col_val_name
        vs_crf_config_ALS['crf_col_val_name'] = np.where(
            vs_crf_config_ALS['crf_col_val_name'].notna() &
            ~vs_crf_config_ALS['crf_col_val_name'].str.contains('^(Yes|No)', case=False, na=False),
            None,
            vs_crf_config_ALS['crf_col_val_name']
        )
        vs_crf_config_ALS = vs_crf_config_ALS.drop_duplicates()

        # Update crf_col_name based on conditions
        conditions = [
            vs_crf_config_ALS['crf_col_desc'].str.contains('Date', case=False, na=False),
            vs_crf_config_ALS['crf_col_desc'].str.contains('^(Was|Were)', case=False, na=False),
            vs_crf_config_ALS['crf_col_desc'].str.contains('Time Point', case=False, na=False),
            vs_crf_config_ALS['crf_col_desc'].str.contains('Temperature', case=False, na=False),
            vs_crf_config_ALS['crf_col_desc'].str.contains('Weight', case=False, na=False)
        ]
        suffixes = ['_INT', '_STD', '_STD', '_STD', '_STD']

        for condition, suffix in zip(conditions, suffixes):
            mask = condition
            vs_crf_config_ALS.loc[mask, 'crf_col_name'] = vs_crf_config_ALS.loc[mask, 'crf_col_name'] + suffix

        # Update r_col_val_name
        vs_crf_config_ALS['r_col_val_name'] = np.where(
            vs_crf_config_ALS['crf_col_val_name'].str.contains('^Yes', case=False, na=False),
            'Yes',
            np.where(
                vs_crf_config_ALS['crf_col_val_name'].str.contains('^No', case=False, na=False),
                'No',
                None
            )
        )

        # Add system variables
        config_systemVar_vs = ['Subject', 'instanceId', 'InstanceName', 'RecordDate', 'LastChangeDate', 'WhatChanged']
        col_des_vs = [
            'Unique subject identifier',
            'Unique folder instance identifier',
            'Name of the folder instance',
            'RecordDate',
            'LastChangeDate',
            'WhatChanged'
        ]

        system_rows = []
        for form in vs_crf_config_ALS['crf_name'].unique():
            for var, desc in zip(config_systemVar_vs, col_des_vs):
                system_rows.append({
                    'study_name': study_name,
                    'crf_name': form,
                    'r_col_name': var,
                    'crf_col_name': var,
                    'r_col_val_name': None,
                    'crf_col_val_name': None,
                    'crf_col_desc': desc
                })

        # Process forms with unit
        forms_with_unit = vs_crf_config_ALS[
            vs_crf_config_ALS['crf_name'].str.contains('^vs', case=False, na=False) &
            vs_crf_config_ALS['crf_col_desc'].str.contains('Temperature|Weight', case=False, na=False)
        ].copy()

        forms_with_unit['temp_unit'] = np.where(
            forms_with_unit['crf_col_desc'].str.contains('Temperature', case=False, na=False),
            forms_with_unit['crf_col_name'] + '_UN',
            None
        )
        forms_with_unit['weight_unit'] = np.where(
            forms_with_unit['crf_col_desc'].str.contains('Weight', case=False, na=False),
            forms_with_unit['crf_col_name'] + '_UN',
            None
        )

        forms_with_unit = forms_with_unit[['crf_name', 'temp_unit', 'weight_unit']]
        unit_rows = []

        for _, row in forms_with_unit.iterrows():
            if pd.notna(row['temp_unit']):
                unit_rows.append({
                    'study_name': study_name,
                    'crf_name': row['crf_name'],
                    'r_col_name': 'temp_unit',
                    'crf_col_name': row['temp_unit'],
                    'r_col_val_name': None,
                    'crf_col_val_name': None,
                    'crf_col_desc': 'Standard unit of temperature'
                })
            if pd.notna(row['weight_unit']):
                unit_rows.append({
                    'study_name': study_name,
                    'crf_name': row['crf_name'],
                    'r_col_name': 'weight_unit',
                    'crf_col_name': row['weight_unit'],
                    'r_col_val_name': None,
                    'crf_col_val_name': None,
                    'crf_col_desc': 'Standard unit of weight'
                })

        # Generate r_name for vs_config
        r_name_vs_gen = vs_crf_config_ALS[['crf_name']].drop_duplicates()
        r_name_vs_gen['category'] = np.where(r_name_vs_gen['crf_name'].str.contains('vs', case=False, na=False), 'vs', 'eg')
        r_name_vs_gen = r_name_vs_gen.sort_values('crf_name').reset_index(drop=True)
        r_name_vs_gen['index'] = r_name_vs_gen.groupby('category').cumcount() + 1
        r_name_vs_gen['r_name'] = r_name_vs_gen['category'] + r_name_vs_gen['index'].astype(str)

        # Combine all components
        vs_crf_config = pd.concat([
            pd.DataFrame(system_rows),
            vs_crf_config_ALS,
            pd.DataFrame(unit_rows)
        ], ignore_index=True)

        # Add crf_col_type
        vs_crf_config['crf_col_type'] = np.where(
            vs_crf_config['crf_col_desc'].str.contains('Date', case=False, na=False),
            'date',
            np.where(
                vs_crf_config['r_col_name'].str.contains('stdvalue|instanceId', case=False, na=False),
                'numeric',
                'character'
            )
        )

        # Sort and join with r_name
        vs_crf_config = vs_crf_config.sort_values(['crf_name', 'r_col_name']).reset_index(drop=True)
        vs_crf_config = vs_crf_config.merge(r_name_vs_gen[['crf_name', 'r_name']], on='crf_name', how='left')

        # Select final columns and filter
        vs_crf_config = vs_crf_config[[
            'study_name', 'r_name', 'crf_name', 'r_col_name', 'crf_col_name',
            'crf_col_type', 'r_col_val_name', 'crf_col_val_name', 'crf_col_desc'
        ]].drop_duplicates()

        vs_crf_config = vs_crf_config[
            vs_crf_config['r_col_name'].notna() &
            vs_crf_config['crf_col_name'].notna()
        ].reset_index(drop=True)

        # Process metadata extraction
        rs_folder_name = als_folders[
            als_folders['FolderName'].str.contains('Response Assessment', case=False, na=False)
        ]['FolderName'].tolist()

        # Extract unscheduled pattern
        unscheduled_patterns = [
            name for name in rs_folder_name
            if re.search('unscheduled', name, re.IGNORECASE)
        ]
        unscheduled_grep = '|'.join(
            [re.search('(unscheduled.*?)(?=\\s|$)', name, re.IGNORECASE).group(1)
             for name in unscheduled_patterns]
        ) if unscheduled_patterns else None

        # Extract EOT pattern
        eot_patterns = [
            name for name in rs_folder_name
            if re.search('end of treatment|eot', name, re.IGNORECASE)
        ]
        if not eot_patterns:
            eot_patterns = [
                name for name in als_folders['FolderName']
                if re.search('end of treatment|eot', name, re.IGNORECASE)
            ]
        eot_grep = '|'.join(
            [re.search('(end of treatment|eot.*?)(?=\\s|$)', name, re.IGNORECASE).group(1)
             for name in eot_patterns]
        ) if eot_patterns else None

        # Extract baseline visit pattern
        baseline_patterns = [
            name for name in als_folders['FolderName']
            if re.search('^screening', name, re.IGNORECASE)
        ]
        baseline_visit_name = '|'.join(
            [re.search('(^screening.*?)(?=\\s|$)', name, re.IGNORECASE).group(1)
             for name in baseline_patterns]
        ) if baseline_patterns else None

        # Extract scheduled visit pattern
        def get_scheduled_pattern(folder_names):
            pattern = None
            if any(re.search(r'C\d+D\d+|cycle\d+day\d+|W\d+D\d+|week\d+day\d+', name, re.IGNORECASE) for name in folder_names):
                matches = []
                for name in folder_names:
                    match = re.search(r'(C\d+D\d+|cycle\d+day\d+|W\d+D\d+|week\d+day\d+)', name, re.IGNORECASE)
                    if match:
                        matches.append(match.group(1))
                pattern = '|'.join(set(matches))
            elif any(re.search(r'week(s)?|cycle', name, re.IGNORECASE) for name in folder_names):
                matches = []
                for name in folder_names:
                    match = re.search(r'(week(s)?|cycle)', name, re.IGNORECASE)
                    if match:
                        matches.append(match.group(1))
                pattern = '|'.join(set(matches))
            elif any(re.search(r'after', name, re.IGNORECASE) for name in folder_names):
                pattern = '(a|A)fter'
            return pattern

        scheduled_grep = get_scheduled_pattern(rs_folder_name)

        # Create da_meta_config
        da_meta_config = pd.DataFrame({
            'study_name': study_name,
            'config_name': [
                'lesion_metric', 'anchor_type', 'study_unit',
                'unscheduled_grep', 'eot_grep', 'scheduled_grep', 'baseline_visit_name'
            ],
            'config_value': [
                lesion_metric, anchor_type, study_unit,
                unscheduled_grep, eot_grep, scheduled_grep, baseline_visit_name
            ]
        })

        # Process CrfGlossary4Spotfire
        crf_glossary = var_label_value[['OID', 'DraftFormName']].copy()
        crf_glossary = crf_glossary[crf_glossary['OID'].notna()].reset_index(drop=True)
        crf_glossary.columns = ['AbbreviatedCRFName', 'CRFname']
        crf_glossary = crf_glossary[crf_glossary['AbbreviatedCRFName'].notna()].reset_index(drop=True)
        crf_glossary = crf_glossary.drop_duplicates().reset_index(drop=True)

        # Add additional rows to CrfGlossary4Spotfire
        additional_rows = pd.DataFrame([
            {'AbbreviatedCRFName': 'dose_merged', 'CRFname': 'Merged dosing forms'},
            {'AbbreviatedCRFName': 'subjectinfo', 'CRFname': 'Important info about subjects'},
            {'AbbreviatedCRFName': 'td_merged', 'CRFname': 'Merged EOT forms'},
            {'AbbreviatedCRFName': 'vs (merged)', 'CRFname': 'Merged vitals forms'},
            {'AbbreviatedCRFName': 'ecg (merged)', 'CRFname': 'Merged ecg forms'},
            {'AbbreviatedCRFName': 'lab', 'CRFname': 'Merged edc and central labs'},
            {'AbbreviatedCRFName': 'ae', 'CRFname': 'Adverse Event(Analysis)'}
        ])
        crf_glossary = pd.concat([crf_glossary, additional_rows], ignore_index=True)

        # Create config files
        wb = Workbook()

        # Add Variable mapping table
        ws = wb.active
        ws.title = "Variable mapping table"
        headers = ['UserDataString', 'OID', 'DraftFormName', 'FieldOID', 'SASLabel']
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)
        for row_idx, row in enumerate(var_label_value.itertuples(), 2):
            for col_idx, value in enumerate(row[1:], 1):
                ws.cell(row=row_idx, column=col_idx, value=value)

        # Add da_crf_config
        ws = wb.create_sheet("da_crf_config")
        headers = ['study_name', 'r_name', 'crf_name', 'r_col_name', 'crf_col_name', 'crf_col_type', 'r_col_val_name', 'crf_col_val_name', 'crf_col_desc']
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)
        for row_idx, row in enumerate(da_crf_config.itertuples(), 2):
            for col_idx, value in enumerate(row[1:], 1):
                ws.cell(row=row_idx, column=col_idx, value=value)

        # Add da_meta_config
        ws = wb.create_sheet("da_meta_config")
        headers = ['study_name', 'config_name', 'config_value']
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)
        for row_idx, row in enumerate(da_meta_config.itertuples(), 2):
            for col_idx, value in enumerate(row[1:], 1):
                ws.cell(row=row_idx, column=col_idx, value=value)

        # Add vs_crf_config
        ws = wb.create_sheet("vs_crf_config")
        headers = ['study_name', 'r_name', 'crf_name', 'r_col_name', 'crf_col_name', 'crf_col_type', 'r_col_val_name', 'crf_col_val_name', 'crf_col_desc']
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)
        for row_idx, row in enumerate(vs_crf_config.itertuples(), 2):
            for col_idx, value in enumerate(row[1:], 1):
                ws.cell(row=row_idx, column=col_idx, value=value)

        # Add CrfGlossary4Spotfire
        ws = wb.create_sheet("CrfGlossary4Spotfire")
        headers = ['AbbreviatedCRFName', 'CRFname']
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)
        for row_idx, row in enumerate(crf_glossary.itertuples(), 2):
            for col_idx, value in enumerate(row[1:], 1):
                ws.cell(row=row_idx, column=col_idx, value=value)

        # Format headers in all sheets
        for sheet in wb.sheetnames:
            ws = wb[sheet]
            for cell in ws[1]:
                cell.font = openpyxl.styles.Font(bold=True)
                cell.fill = openpyxl.styles.PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
                cell.alignment = openpyxl.styles.Alignment(horizontal="center")
                cell.border = openpyxl.styles.Border(
                    left=openpyxl.styles.Side(style='thin'),
                    right=openpyxl.styles.Side(style='thin'),
                    top=openpyxl.styles.Side(style='thin'),
                    bottom=openpyxl.styles.Side(style='thin')
                )

        # Save the workbook
        output_path = f"config_combo_{study_name}.xlsx"
        wb.save(output_path)

        # Store study name in session state
        st.session_state.study_name = study_name

        return output_path

    except Exception as e:
        raise Exception(f"Error generating config combo: {str(e)}")

def generate_drug_merge_info(als_file):
    """Generate drug merge info from ALS file."""
    try:
        # Save the uploaded file temporarily
        with open("temp_als_file.xlsx", "wb") as f:
            f.write(als_file.getvalue())

        # Read ALS file sheets with explicit engine
        fields_df = pd.read_excel("temp_als_file.xlsx", sheet_name="Fields", engine='openpyxl')
        forms_df = pd.read_excel("temp_als_file.xlsx", sheet_name="Forms", engine='openpyxl')

        # Clean up temporary file
        Path("temp_als_file.xlsx").unlink()

        # Process fields
        fields_df = fields_df[['FormOID', 'FieldOID', 'SASLabel']].copy()
        fields_df['Form'] = fields_df['FormOID'].str.lower()
        fields_df = fields_df.drop('FormOID', axis=1).dropna()

        # Process forms
        forms_df = forms_df[['OID', 'DraftFormName']].copy()
        forms_df['Form'] = forms_df['OID'].str.lower()
        forms_df = forms_df.drop('OID', axis=1).dropna()

        # Join fields and forms
        var_label_form_drug = fields_df.merge(forms_df, on='Form')
        var_label_form_drug = var_label_form_drug[
            var_label_form_drug['DraftFormName'].str.contains('Study Drug Administration') &
            ~var_label_form_drug['FieldOID'].str.contains('RSG')
        ]

        # Required columns for dose_merged
        col_req = [
            "Subject name or identifier",
            "Dose Start Date (Interpolated)",
            "Dose End Date (Interpolated)",
            "Planned Dose",
            "Actual Dose",
            "Actual Frequency",
            "Planned Frequency",
            "Other acutual dose",
            "Other planned dose",
            "Other actual frequency",
            "Other planned frequency",
            "Cycle Number",
            "Was Study Drug administered?",
            "Reason for not administered",
            "Specify Other for Reason not administered",
            "Was Dose Modified?",
            "Reason Dose Modified",
            "Specify Other for Reason Dose Modified",
            "Was Dose Missed?",
            "Reason Dose Missed",
            "Specify Other for Reason Dose Missed",
            "Was Dose delayed since the last dose?",
            "Reason Dose delayed",
            "Specify Other for Reason Dose delayed",
            "Was Dose interrupted?",
            "Reason Dose interrupted",
            "Specify Other for Reason Dose interrupted",
            "Was Dose Discontinued?",
            "Reason Dose Discontinued",
            "Specify Other for Reason Dose Discontinued",
            "Was Dose Decreased?",
            "Reason Dose Decreased",
            "Specify Other for Reason Dose Decreased"
        ]

        # Create drug index
        drug_index = var_label_form_drug[['Form']].drop_duplicates()
        drug_index['index'] = range(1, len(drug_index) + 1)
        drug_index['drug_number'] = 'drug' + drug_index['index'].astype(str)
        drug_index['dose_number'] = 'dose' + drug_index['index'].astype(str)
        drug_index = drug_index.drop('index', axis=1)

        # Map fields to standardized labels
        drug_merged_info = drug_index.merge(var_label_form_drug, on='Form')
        drug_merged_info['Treatment'] = drug_merged_info['DraftFormName'].str.extract(r'.*-\s*(.*)')

        def map_label(row):
            sas_label = str(row['SASLabel']).lower()
            if 'start date' in sas_label:
                return "Dose Start Date (Interpolated)"
            elif any(x in sas_label for x in ['stop date', 'end date']):
                return "Dose End Date (Interpolated)"
            elif sas_label.startswith('actual dose'):
                return "Actual Dose"
            elif any(sas_label.startswith(x) for x in ['planned dose', 'prescribed dose']):
                return "Planned Dose"
            elif 'actual frequency' in sas_label:
                return "Actual Frequency"
            elif any(x in sas_label for x in ['planned frequency', 'prescribed frequency']):
                return "Planned Frequency"
            elif re.search(r'was .+ (administered|dosed)', sas_label):
                return "Was Study Drug administered?"
            elif re.search(r'reason for .* not (administered|dosed)', sas_label):
                return "Reason for not administered"
            elif 'other' in sas_label:
                return row['SASLabel']
            elif re.search(r'did subject report any dosing errors|was .+ missed', sas_label):
                return "Was Dose Missed?"
            elif re.search(r'reason for .* miss', sas_label):
                return "Reason Dose Missed"
            elif re.search(r'was .+ delayed', sas_label):
                return "Was Dose delayed since the last dose?"
            elif re.search(r'reason for .* delay', sas_label):
                return "Reason Dose delayed"
            elif re.search(r'was .+ interrupt', sas_label):
                return "Was Dose interrupted?"
            elif re.search(r'reason for interruption', sas_label):
                return "Reason Dose interrupted"
            elif re.search(r'has .+ been modified|was .+ modified', sas_label):
                return "Was Dose Modified?"
            elif re.search(r'reason for modification', sas_label):
                return "Reason Dose Modified"
            elif re.search(r'was .+ decrease', sas_label):
                return "Was Dose Decreased?"
            elif re.search(r'reason for .* decrease', sas_label):
                return "Reason Dose Decreased"
            elif re.search(r'was .+ discontinue', sas_label):
                return "Was Dose Discontinued?"
            elif re.search(r'reason for .* discontinue', sas_label):
                return "Reason Dose Discontinued"
            return None

        drug_merged_info['label_doseMerged'] = drug_merged_info.apply(map_label, axis=1)
        drug_merged_info = drug_merged_info.dropna(subset=['label_doseMerged'])
        drug_merged_info = drug_merged_info[~drug_merged_info['label_doseMerged'].str.contains('Unit', case=False)]

        # Handle "Other specify" labels
        drug_merged_info['pre_val'] = drug_merged_info['label_doseMerged'].shift(1)
        drug_merged_info['label_doseMerged'] = drug_merged_info.apply(
            lambda row: (
                f"Specify Other for {row['pre_val']}" if 'Other' in str(row['label_doseMerged']) and 'Reason' in str(row['pre_val'])
                else "Other acutual dose" if 'Other' in str(row['label_doseMerged']) and 'Actual Dose' in str(row['pre_val'])
                else "Other planned dose" if 'Other' in str(row['label_doseMerged']) and any(x in str(row['pre_val']) for x in ['Prescribed Dose', 'Planned Dose'])
                else "Other actual frequency" if 'Other' in str(row['label_doseMerged']) and 'Actual Frequency' in str(row['pre_val'])
                else "Other planned frequency" if 'Other' in str(row['label_doseMerged']) and 'Planned Frequency' in str(row['pre_val'])
                else row['label_doseMerged']
            ),
            axis=1
        )
        drug_merged_info = drug_merged_info.drop('pre_val', axis=1)
        drug_merged_info = drug_merged_info[~drug_merged_info['label_doseMerged'].str.contains('Other, specify', case=False)]

        # Create full table
        drug_full_tab = pd.DataFrame({
            'Form': np.repeat(drug_index['Form'], len(col_req)),
            'drug_number': np.repeat(drug_index['drug_number'], len(col_req)),
            'dose_number': np.repeat(drug_index['dose_number'], len(col_req)),
            'label_doseMerged': col_req * len(drug_index)
        })

        # Merge with drug_merged_info
        drug_full_tab = drug_full_tab.merge(
            drug_merged_info[['Form', 'FieldOID', 'label_doseMerged']],
            on=['Form', 'label_doseMerged'],
            how='left'
        )

        # Set default values
        drug_full_tab['FieldOID'] = drug_full_tab.apply(
            lambda row: (
                'Subject' if row['label_doseMerged'] == "Subject name or identifier"
                else 'Folder' if row['label_doseMerged'] == "Cycle Number"
                else row['FieldOID']
            ),
            axis=1
        )

        # Add Treatment
        drug_full_tab = drug_full_tab.merge(
            drug_merged_info[['Form', 'Treatment']].drop_duplicates(),
            on='Form',
            how='left'
        )

        # Set NA_character_ for missing values
        drug_full_tab['FieldOID'] = drug_full_tab.apply(
            lambda row: (
                row['Treatment'] if pd.isna(row['FieldOID']) and row['label_doseMerged'] == "Treatment"
                else "NA_character_" if pd.isna(row['FieldOID'])
                else row['FieldOID']
            ),
            axis=1
        )

        return drug_full_tab

    except Exception as e:
        st.error(f"Error generating drug merge info: {str(e)}")
        return None

def load_r_template(template_name):
    """Load R template file content."""
    try:
        with open(template_name, 'r') as f:
            return f.read()
    except Exception as e:
        raise Exception(f"Error loading R template: {str(e)}")

def generate_ae_cm_link_function(template_code, study_id, variable_mappings):
    """Generate AE_CMlink function with replaced variables and study ID."""
    # Replace the function name and title
    new_function_name = f"ae_1_linkAECMColumn_{study_id}"
    modified_code = re.sub(r'ae_1_linkAECMColumn_b_bgb_b3227_101test', new_function_name, template_code)

    # Replace variables using the mapping dictionary
    modified_code, replacements_made = replace_variables(modified_code, variable_mappings)

    return modified_code, replacements_made

def generate_sae_flag_function(template_code, study_id, variable_mappings):
    """Generate SAE Flag function with replaced variables and study ID."""
    # Replace the function name and title
    new_function_name = f"ae_3_SAEFlag_{study_id}"
    modified_code = re.sub(r'ae_3_SAEFlag_b_bgb_b3227_101test', new_function_name, template_code)

    # Replace variables using the mapping dictionary
    modified_code, replacements_made = replace_variables(modified_code, variable_mappings)

    return modified_code, replacements_made

def generate_mh_flag_function(template_code, study_id, variable_mappings):
    """Generate MH Flag function with replaced variables and study ID."""
    # Replace the function name and title
    new_function_name = f"ae_4_MHFlag_{study_id}"
    modified_code = re.sub(r'ae_4_MHFlag_b_bgb_b3227_101test', new_function_name, template_code)

    # Replace variables using the mapping dictionary
    modified_code, replacements_made = replace_variables(modified_code, variable_mappings)

    return modified_code, replacements_made

def generate_vital_ctcae_function(template_code, study_id, variable_mappings):
    """Generate Vital CTCAE function with replaced variables and study ID."""
    # Replace all three function names in the template
    # 1. Replace vitalctcae_config function name
    modified_code = re.sub(r'vitalctcae_config_b_bgb_b3227_101test', f'vitalctcae_config_{study_id}', template_code)

    # 2. Replace vitalctcae_datastandardization function name
    modified_code = re.sub(r'vitalctcae_datastandardization_b_bgb_b3227_101test', f'vitalctcae_datastandardization_{study_id}', modified_code)

    # 3. Replace vitalctcae_grading function name
    modified_code = re.sub(r'vitalctcae_grading_b_bgb_b3227_101test', f'vitalctcae_grading_{study_id}', modified_code)

    # Replace variables using the mapping dictionary
    modified_code, replacements_made = replace_variables(modified_code, variable_mappings)

    return modified_code, replacements_made

def generate_aetox_flag_function(template_code, study_id, variable_mappings):
    # Get the function name from the template code
    if "ae_2_ToxFlag_b_bgb_b3227_101test" in template_code:
        new_function_name = f"ae_2_ToxFlag_{study_id}"
        modified_code = template_code.replace("ae_2_ToxFlag_b_bgb_b3227_101test", new_function_name)
    else:
        new_function_name = f"ae_2_ToxFlag_{study_id}"
        modified_code = template_code.replace("ae_2_ToxFlag_b_bgb_3111_101", new_function_name)

    # Replace variables in the template code
    modified_code, replacements = replace_variables(modified_code, variable_mappings)

    return modified_code, replacements

def generate_labcalc_function(template_code, study_id, variable_mappings):
    # Get the function name from the template code
    if "labCalc_b_bgb_b3227_101test" in template_code:
        new_function_name = f"labCalc_{study_id}"
        modified_code = template_code.replace("labCalc_b_bgb_b3227_101test", new_function_name)
    else:
        new_function_name = f"labCalc_{study_id}"
        modified_code = template_code.replace("labCalc_b_bgb_3111_101", new_function_name)

    # Replace variables in the template code
    modified_code, replacements = replace_variables(modified_code, variable_mappings)

    return modified_code, replacements

def collect_all_generated_files():
    """Collect all confirmed files from session state."""
    all_files = {}

    # Collect config files if they exist
    if 'study_name' in st.session_state:
        study_name = st.session_state.study_name
        config_file_name = f"config_combo_{study_name}.xlsx"

        # Check if config file exists in session state
        if 'config_file_data' in st.session_state:
            # Config file is stored as binary data, so we handle it separately
            all_files[config_file_name] = st.session_state.config_file_data

    # Collect all confirmed R codes from session state
    if 'confirmed_files' not in st.session_state:
        st.session_state.confirmed_files = {}

    # Add confirmed files to the collection
    for filename, content in st.session_state.confirmed_files.items():
        all_files[filename] = content

    return all_files

def display_function_confirmation_ui(function_key, display_key_suffix=""):
    """Helper function to display function confirmation UI."""
    if 'generated_functions' in st.session_state and function_key in st.session_state.generated_functions:
        function_info = st.session_state.generated_functions[function_key]

        # Display the generated code in an editable text area
        st.subheader("Generated R Function")
        edited_code = st.text_area(
            "Review and edit the generated R function if needed:",
            value=function_info['code'],
            height=500,
            key=f"{function_key}_code_display{display_key_suffix}"
        )

        # Check if this function has been confirmed
        filename = function_info['filename']
        is_confirmed = filename in st.session_state.get('confirmed_files', {})

        col1, col2 = st.columns([1, 1])
        with col1:
            if st.button("✅ Confirm Function", key=f"confirm_{function_key}{display_key_suffix}", disabled=is_confirmed):
                if 'confirmed_files' not in st.session_state:
                    st.session_state.confirmed_files = {}
                st.session_state.confirmed_files[filename] = edited_code
                st.success(f"✅ {function_info['title']} confirmed and ready for download!")
                # Update the confirmation status immediately
                is_confirmed = True

        with col2:
            if is_confirmed:
                st.markdown('<div class="status-confirmed">✅ Function Confirmed</div>', unsafe_allow_html=True)
            else:
                st.markdown('<div class="status-pending">⏳ Awaiting Confirmation</div>', unsafe_allow_html=True)

        return True
    else:
        st.info("Please generate config files first to automatically generate all functions.")
        return False

def generate_all_functions():
    """Generate all R functions automatically after config files are created."""
    if not st.session_state.als_file or not st.session_state.tumor_type or 'study_name' not in st.session_state:
        return False

    try:
        study_id = st.session_state.study_name

        # Initialize generated functions storage
        if 'generated_functions' not in st.session_state:
            st.session_state.generated_functions = {}

        # 1. Generate Dose Merge Function
        if 'drug_merge_info' in st.session_state:
            dose_merge_generator = DoseMergeGenerator(st.session_state.drug_merge_info)
            st.session_state.generated_functions['dose_merge'] = {
                'code': dose_merge_generator.generate_function(study_id),
                'filename': f"doseMerge_{study_id}.R",
                'title': "Dose Merge Function"
            }

        # 2. Generate TD Merge Function
        td_merge_generator = TDMergeGenerator(st.session_state.als_file)
        st.session_state.generated_functions['td_merge'] = {
            'code': td_merge_generator.generate_function(study_id),
            'filename': f"tdMerge_{study_id}.R",
            'title': "TD Merge Function"
        }

        # 3. Generate Subject Info Function
        subject_info_generator = SubjectInfoGenerator(st.session_state.als_file, st.session_state.tumor_type)
        st.session_state.generated_functions['subject_info'] = {
            'code': subject_info_generator.generate_function(study_id),
            'filename': f"subjectInfo_{study_id}.R",
            'title': "Subject Info Function"
        }

        # 4. Generate Patient Profile Function
        patient_profile_generator = PatientProfileGenerator(st.session_state.als_file, st.session_state.tumor_type)
        st.session_state.generated_functions['patient_profile'] = {
            'code': patient_profile_generator.generate_function(study_id),
            'filename': f"patientProfile_{study_id}.R",
            'title': "Patient Profile Function"
        }

        # 5. Generate AE WriteOut Function
        ae_writeout_generator = AEWriteOutGenerator(st.session_state.als_file, st.session_state.tumor_type)
        st.session_state.generated_functions['ae_writeout'] = {
            'code': ae_writeout_generator.generate_function(study_id),
            'filename': f"ae_5_AEWriteOut_{study_id}.R",
            'title': "AE WriteOut Function"
        }

        # 6. Generate Controller Function
        controller_generator = ControllerGenerator(st.session_state.als_file, study_id, st.session_state.tumor_type)
        controller_code = controller_generator.generate_controller()
        if controller_code:
            st.session_state.generated_functions['controller'] = {
                'code': controller_code,
                'filename': f"controllerMDR_{study_id}.R",
                'title': "Controller Function"
            }

        # 7-12. Generate functions that require config file
        if 'var_label_value' in st.session_state:
            try:
                config_df = load_config_file()
                variable_mappings, _ = find_matching_variables(st.session_state.var_label_value, config_df)

                if variable_mappings:
                    # AE_CMlink Function
                    template_code = load_r_template("//10.8.52.10/Spotfire/MDR_Auto/config_var_replace/mdr_R_package_template/ae_1_linkAECMColumn_b_bgb_b3227_101test.R")
                    modified_code, _ = generate_ae_cm_link_function(template_code, study_id, variable_mappings)
                    st.session_state.generated_functions['ae_cm_link'] = {
                        'code': modified_code,
                        'filename': f"ae_1_linkAECMColumn_{study_id}.R",
                        'title': "AE CM Link Function"
                    }

                    # SAE Flag Function
                    template_code = load_r_template("//10.8.52.10/Spotfire/MDR_Auto/config_var_replace/mdr_R_package_template/ae_3_SAEFlag_b_bgb_b3227_101test.R")
                    modified_code, _ = generate_sae_flag_function(template_code, study_id, variable_mappings)
                    st.session_state.generated_functions['sae_flag'] = {
                        'code': modified_code,
                        'filename': f"ae_3_SAEFlag_{study_id}.R",
                        'title': "SAE Flag Function"
                    }

                    # MH Flag Function
                    template_code = load_r_template("//10.8.52.10/Spotfire/MDR_Auto/config_var_replace/mdr_R_package_template/ae_4_MHFlag_b_bgb_b3227_101test.R")
                    modified_code, _ = generate_mh_flag_function(template_code, study_id, variable_mappings)
                    st.session_state.generated_functions['mh_flag'] = {
                        'code': modified_code,
                        'filename': f"ae_4_MHFlag_{study_id}.R",
                        'title': "MH Flag Function"
                    }

                    # Vital CTCAE Function
                    template_code = load_r_template("//10.8.52.10/Spotfire/MDR_Auto/config_var_replace/mdr_R_package_template/vitalctcae_b_bgb_b3227_101test.R")
                    modified_code, _ = generate_vital_ctcae_function(template_code, study_id, variable_mappings)
                    st.session_state.generated_functions['vital_ctcae'] = {
                        'code': modified_code,
                        'filename': f"vitalctcae_{study_id}.R",
                        'title': "Vital CTCAE Functions (Config, Data Standardization, Grading)"
                    }

                    # aeTox Flag Function
                    if st.session_state.tumor_type == "Heme":
                        template_code = load_r_template("//10.8.52.10/Spotfire/MDR_Auto/config_var_replace/mdr_R_package_template/ae_2_ToxFlag_b_bgb_3111_101.R")
                    else:
                        template_code = load_r_template("//10.8.52.10/Spotfire/MDR_Auto/config_var_replace/mdr_R_package_template/ae_2_ToxFlag_b_bgb_b3227_101test.R")
                    modified_code, _ = generate_aetox_flag_function(template_code, study_id, variable_mappings)
                    st.session_state.generated_functions['aetox_flag'] = {
                        'code': modified_code,
                        'filename': f"ae_2_ToxFlag_{study_id}.R",
                        'title': "aeTox Flag Function"
                    }

                    # labCalc Function
                    if st.session_state.tumor_type == "Heme":
                        template_code = load_r_template("//10.8.52.10/Spotfire/MDR_Auto/config_var_replace/mdr_R_package_template/labCalc_b_bgb_3111_101.R")
                    else:
                        template_code = load_r_template("//10.8.52.10/Spotfire/MDR_Auto/config_var_replace/mdr_R_package_template/labCalc_b_bgb_b3227_101test.R")
                    modified_code, _ = generate_labcalc_function(template_code, study_id, variable_mappings)
                    st.session_state.generated_functions['labcalc'] = {
                        'code': modified_code,
                        'filename': f"labCalc_{study_id}.R",
                        'title': "labCalc Function"
                    }
            except Exception as e:
                st.warning(f"Some functions could not be generated due to missing templates or config: {str(e)}")

        return True

    except Exception as e:
        st.error(f"Error generating functions: {str(e)}")
        return False

def apply_custom_css():
    """Apply custom CSS styling to make the app more visually appealing."""
    st.markdown("""
    <style>
    /* Import Google Fonts */
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap');

    /* Main app styling */
    .stApp {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        font-family: 'Inter', sans-serif;
    }

    /* Expand content area width */
    .css-18e3th9, .css-1d391kg, .css-1lcbmhc {
        padding-top: 1rem;
        padding-bottom: 1rem;
    }

    /* Remove default padding from main container */
    .css-k1vhr4, .css-1kyxreq {
        padding-top: 0rem;
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }

    /* Maximize content width */
    .main > div {
        max-width: none;
        padding-left: 1rem;
        padding-right: 1rem;
    }

    /* Main content area */
    .main .block-container {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 15px;
        padding: 1rem 1.5rem;
        margin-top: 0.5rem;
        margin-left: 0.5rem;
        margin-right: 0.5rem;
        max-width: none;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    /* Title styling */
    h1 {
        color: #2c3e50 !important;
        font-family: 'Inter', sans-serif !important;
        font-weight: 700 !important;
        font-size: 2.5rem !important;
        text-align: center !important;
        margin-top: 0.5rem !important;
        margin-bottom: 1rem !important;
        text-shadow: 0 2px 4px rgba(44, 62, 80, 0.1) !important;
        letter-spacing: -0.02em !important;
    }

    /* Subtitle styling */
    h2 {
        color: #34495e;
        font-family: 'Inter', sans-serif;
        font-weight: 600;
        font-size: 1.6rem !important;
        margin-top: 1rem !important;
        margin-bottom: 1rem !important;
        border-bottom: 3px solid #667eea;
        padding-bottom: 0.5rem;
    }

    h3 {
        color: #2c3e50;
        font-family: 'Inter', sans-serif;
        font-weight: 500;
        font-size: 1.3rem !important;
        margin-top: 0.5rem !important;
        margin-bottom: 0.75rem !important;
    }

    /* Sidebar styling */
    .css-1d391kg {
        background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
        border-radius: 0 20px 20px 0;
    }

    .css-1d391kg .css-1v0mbdj {
        color: white;
    }

    /* Sidebar title */
    .css-1d391kg h1 {
        color: white !important;
        background: none !important;
        font-size: 1.5rem !important;
        text-align: left !important;
        margin-bottom: 1rem !important;
        font-weight: 600 !important;
        text-shadow: none !important;
    }

    /* Sidebar radio buttons */
    .css-1d391kg .stRadio > label {
        color: white !important;
        font-weight: 500;
    }

    .css-1d391kg .stRadio > div {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 10px;
        padding: 0.5rem;
        margin-bottom: 0.5rem;
    }

    /* Sidebar download section */
    .css-1d391kg .stMarkdown {
        color: white !important;
    }

    /* Button styling */
    .stButton > button {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border: none;
        border-radius: 12px;
        padding: 0.75rem 1.5rem;
        font-family: 'Inter', sans-serif;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    .stButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        background: linear-gradient(135deg, #764ba2, #667eea);
    }

    .stButton > button:active {
        transform: translateY(0);
    }

    /* Download button special styling */
    .stDownloadButton > button {
        background: linear-gradient(135deg, #27ae60, #2ecc71);
        color: white;
        border: none;
        border-radius: 12px;
        padding: 0.75rem 1.5rem;
        font-family: 'Inter', sans-serif;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
    }

    .stDownloadButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(39, 174, 96, 0.4);
        background: linear-gradient(135deg, #2ecc71, #27ae60);
    }

    /* File uploader styling */
    .stFileUploader {
        background: rgba(102, 126, 234, 0.05);
        border: 2px dashed #667eea;
        border-radius: 15px;
        padding: 2rem;
        text-align: center;
        transition: all 0.3s ease;
    }

    .stFileUploader:hover {
        background: rgba(102, 126, 234, 0.1);
        border-color: #764ba2;
    }

    /* File uploader label styling */
    .stFileUploader label {
        font-size: 1.3rem !important;
        font-weight: 700 !important;
        color: #2c3e50 !important;
        margin-bottom: 1rem !important;
    }

    /* Select box styling */
    .stSelectbox > div > div {
        background: white;
        border: 2px solid #e1e8ed;
        border-radius: 12px;
        font-family: 'Inter', sans-serif;
        transition: all 0.3s ease;
    }

    .stSelectbox > div > div:focus-within {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    /* Select box label styling */
    .stSelectbox label {
        font-size: 1.3rem !important;
        font-weight: 700 !important;
        color: #2c3e50 !important;
        margin-bottom: 1rem !important;
    }

    /* Text area styling */
    .stTextArea > div > div > textarea {
        background: #f8f9fa;
        border: 2px solid #e1e8ed;
        border-radius: 12px;
        font-family: 'JetBrains Mono', monospace;
        font-size: 0.9rem;
        line-height: 1.5;
        transition: all 0.3s ease;
    }

    .stTextArea > div > div > textarea:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    /* Success/Info/Warning message styling */
    .stSuccess {
        background: linear-gradient(135deg, #d4edda, #c3e6cb);
        border: none;
        border-radius: 12px;
        color: #155724;
        font-weight: 500;
    }

    .stInfo {
        background: linear-gradient(135deg, #d1ecf1, #bee5eb);
        border: none;
        border-radius: 12px;
        color: #0c5460;
        font-weight: 500;
    }

    .stWarning {
        background: linear-gradient(135deg, #fff3cd, #ffeaa7);
        border: none;
        border-radius: 12px;
        color: #856404;
        font-weight: 500;
    }

    .stError {
        background: linear-gradient(135deg, #f8d7da, #f5c6cb);
        border: none;
        border-radius: 12px;
        color: #721c24;
        font-weight: 500;
    }

    /* Dataframe styling */
    .stDataFrame {
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    /* Expander styling */
    .streamlit-expanderHeader {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 12px;
        font-weight: 600;
        color: #2c3e50;
    }

    /* Columns styling */
    .css-1r6slb0 {
        background: rgba(255, 255, 255, 0.5);
        border-radius: 15px;
        padding: 1rem;
        margin: 0.5rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }

    /* Spinner styling */
    .stSpinner > div {
        border-top-color: #667eea !important;
    }

    /* Custom status badges */
    .status-confirmed {
        background: linear-gradient(135deg, #27ae60, #2ecc71);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: 600;
        display: inline-block;
        margin: 0.5rem 0;
    }

    .status-pending {
        background: linear-gradient(135deg, #f39c12, #e67e22);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: 600;
        display: inline-block;
        margin: 0.5rem 0;
    }

    /* Hide Streamlit branding */
    #MainMenu {visibility: hidden;}
    footer {visibility: hidden;}
    header {visibility: hidden;}

    /* Custom scrollbar */
    ::-webkit-scrollbar {
        width: 8px;
    }

    ::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 10px;
    }

    ::-webkit-scrollbar-thumb {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-radius: 10px;
    }

    ::-webkit-scrollbar-thumb:hover {
        background: linear-gradient(135deg, #764ba2, #667eea);
    }
    </style>
    """, unsafe_allow_html=True)

def main():
    # Apply custom CSS styling
    apply_custom_css()

    st.title("MDR R Package Generator")

    # Initialize session state for ALS file and tumor type if not exists
    if 'als_file' not in st.session_state:
        st.session_state.als_file = None
    if 'tumor_type' not in st.session_state:
        st.session_state.tumor_type = None

    # Create sidebar for tab selection
    st.sidebar.title("Navigation")
    selected_tab = st.sidebar.radio(
        "Select Function",
        [
            "Cover Page",
            "Config File Generator",
            "Dose Merge Generator",
            "TD Merge Generator",
            "SubjectInfo Generator",
            "PatientProfile Generator",
            "AEWriteOut Generator",
            "AE_CMlink Generator",
            "SAE Flag Generator",
            "MH Flag Generator",
            "Vital CTCAE Generator",
            "aeTox Flag Generator",
            "labCalc Generator",
            "Controller Generator"
        ]
    )

    # Add download all files section in sidebar
    st.sidebar.markdown("---")
    st.sidebar.subheader("📦 Download All Files")

    # Check if any files have been generated
    all_generated_files = collect_all_generated_files()

    if all_generated_files:
        st.sidebar.success(f"✅ {len(all_generated_files)} files ready")

        # Show list of available files
        with st.sidebar.expander("View Available Files"):
            for filename in all_generated_files.keys():
                st.write(f"• {filename}")

        # Create zip file and download button
        zip_data = create_zip_of_modified_files(all_generated_files)
        study_name = st.session_state.get('study_name', 'generated_files')

        st.sidebar.download_button(
            label="🗂️ Download All Generated Files",
            data=zip_data,
            file_name=f"{study_name}_complete_package.zip",
            mime="application/zip",
            help="Download all CRF config files and generated R codes in one zip file"
        )
    else:
        st.sidebar.info("No files generated yet")
        st.sidebar.write("Generate files using the functions above to enable download.")

    # Display content based on selected tab
    if selected_tab == "Cover Page":
        # Add enhanced welcome section
        st.markdown("""
        <div style="text-align: center; padding: 2rem 0;">
            <h2 style="color: #2c3e50; font-size: 2.5rem; margin-bottom: 1rem;">
                🧬 Welcome to MDR R Package Generator
            </h2>
            <p style="font-size: 1.2rem; color: #7f8c8d; margin-bottom: 2rem; line-height: 1.6;">
                Streamline your clinical trial data analysis with automated R function generation.<br>
                Transform your ALS files into comprehensive R packages with just a few clicks.
            </p>
        </div>
        """, unsafe_allow_html=True)

        # Add feature highlights
        st.markdown("""
        <div style="display: flex; justify-content: space-around; margin: 2rem 0; flex-wrap: wrap;">
            <div style="text-align: center; padding: 1rem; margin: 0.5rem; background: linear-gradient(135deg, #e8f4fd, #d6eaf8); border-radius: 15px; flex: 1; min-width: 200px;">
                <div style="font-size: 2rem; margin-bottom: 0.5rem;">⚡</div>
                <h4 style="color: #2c3e50; margin-bottom: 0.5rem;">Automated Generation</h4>
                <p style="color: #7f8c8d; font-size: 0.9rem;">Generate all R functions automatically from your ALS file</p>
            </div>
            <div style="text-align: center; padding: 1rem; margin: 0.5rem; background: linear-gradient(135deg, #e8f8f5, #d5f4e6); border-radius: 15px; flex: 1; min-width: 200px;">
                <div style="font-size: 2rem; margin-bottom: 0.5rem;">🔍</div>
                <h4 style="color: #2c3e50; margin-bottom: 0.5rem;">Review & Confirm</h4>
                <p style="color: #7f8c8d; font-size: 0.9rem;">Review and customize each function before download</p>
            </div>
            <div style="text-align: center; padding: 1rem; margin: 0.5rem; background: linear-gradient(135deg, #fef9e7, #fcf3cf); border-radius: 15px; flex: 1; min-width: 200px;">
                <div style="font-size: 2rem; margin-bottom: 0.5rem;">📦</div>
                <h4 style="color: #2c3e50; margin-bottom: 0.5rem;">One-Click Download</h4>
                <p style="color: #7f8c8d; font-size: 0.9rem;">Download complete package with all files</p>
            </div>
        </div>
        """, unsafe_allow_html=True)

        st.markdown("---")

        # Getting started section
        st.markdown("""
        <h3 style="color: #2c3e50; text-align: center; margin: 2rem 0 1rem 0;">
            🚀 Getting Started
        </h3>
        """, unsafe_allow_html=True)

        # ALS file upload with custom label
        st.markdown("""
        <div style="margin-bottom: 0.5rem;">
            <label style="font-size: 1.8rem; font-weight: 800; color: #2c3e50; display: block; margin-bottom: 0.5rem;">
                📁 Upload ALS Excel File
            </label>
        </div>
        """, unsafe_allow_html=True)
        uploaded_als_file = st.file_uploader("", type=['xlsx'], label_visibility="collapsed")
        if uploaded_als_file:
            st.session_state.als_file = uploaded_als_file
            st.success("ALS file uploaded successfully!")

        # Tumor type selection with custom label
        st.markdown("""
        <div style="margin-bottom: 0.5rem;">
            <label style="font-size: 1.8rem; font-weight: 800; color: #2c3e50; display: block; margin-bottom: 0.5rem;">
                🧬 Select Tumor Type
            </label>
        </div>
        """, unsafe_allow_html=True)
        tumor_type = st.selectbox(
            "",
            options=["Select...", "Heme", "Solid Tumor"],
            label_visibility="collapsed"
        )
        if tumor_type != "Select...":
            st.session_state.tumor_type = tumor_type
            st.success(f"Tumor type set to: {tumor_type}")
        else:
            st.session_state.tumor_type = None

        # Show current settings with enhanced styling
        st.markdown("""
        <h3 style="color: #2c3e50; text-align: center; margin: 2rem 0 1rem 0;">
            📊 Current Settings
        </h3>
        """, unsafe_allow_html=True)

        col1, col2 = st.columns(2)
        with col1:
            if st.session_state.als_file:
                st.markdown("""
                <div style="background: linear-gradient(135deg, #d4edda, #c3e6cb); padding: 1rem; border-radius: 12px; text-align: center; margin: 0.5rem 0;">
                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">📁</div>
                    <h4 style="color: #155724; margin-bottom: 0.5rem;">ALS File</h4>
                    <p style="color: #155724; font-weight: 600;">✅ Uploaded Successfully</p>
                </div>
                """, unsafe_allow_html=True)
            else:
                st.markdown("""
                <div style="background: linear-gradient(135deg, #f8d7da, #f5c6cb); padding: 1rem; border-radius: 12px; text-align: center; margin: 0.5rem 0;">
                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">📁</div>
                    <h4 style="color: #721c24; margin-bottom: 0.5rem;">ALS File</h4>
                    <p style="color: #721c24; font-weight: 600;">❌ Not Uploaded</p>
                </div>
                """, unsafe_allow_html=True)

        with col2:
            if st.session_state.tumor_type:
                st.markdown(f"""
                <div style="background: linear-gradient(135deg, #d4edda, #c3e6cb); padding: 1rem; border-radius: 12px; text-align: center; margin: 0.5rem 0;">
                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">🧬</div>
                    <h4 style="color: #155724; margin-bottom: 0.5rem;">Tumor Type</h4>
                    <p style="color: #155724; font-weight: 600;">✅ {st.session_state.tumor_type}</p>
                </div>
                """, unsafe_allow_html=True)
            else:
                st.markdown("""
                <div style="background: linear-gradient(135deg, #f8d7da, #f5c6cb); padding: 1rem; border-radius: 12px; text-align: center; margin: 0.5rem 0;">
                    <div style="font-size: 2rem; margin-bottom: 0.5rem;">🧬</div>
                    <h4 style="color: #721c24; margin-bottom: 0.5rem;">Tumor Type</h4>
                    <p style="color: #721c24; font-weight: 600;">❌ Not Selected</p>
                </div>
                """, unsafe_allow_html=True)

        # Add enhanced instructions
        st.markdown("<br>", unsafe_allow_html=True)
        if st.session_state.als_file and st.session_state.tumor_type:
            st.markdown("""
            <div style="background: linear-gradient(135deg, #d1ecf1, #bee5eb); padding: 1.5rem; border-radius: 15px; text-align: center; margin: 1rem 0;">
                <div style="font-size: 2.5rem; margin-bottom: 1rem;">🎉</div>
                <h3 style="color: #0c5460; margin-bottom: 1rem;">Ready to Generate!</h3>
                <p style="color: #0c5460; font-size: 1.1rem; margin-bottom: 0;">
                    Navigate to <strong>Config File Generator</strong> to start creating your R package.
                </p>
            </div>
            """, unsafe_allow_html=True)
        else:
            st.markdown("""
            <div style="background: linear-gradient(135deg, #fff3cd, #ffeaa7); padding: 1.5rem; border-radius: 15px; text-align: center; margin: 1rem 0;">
                <div style="font-size: 2.5rem; margin-bottom: 1rem;">⚠️</div>
                <h3 style="color: #856404; margin-bottom: 1rem;">Setup Required</h3>
                <p style="color: #856404; font-size: 1.1rem; margin-bottom: 0;">
                    Please upload an ALS file and select a tumor type to proceed.
                </p>
            </div>
            """, unsafe_allow_html=True)

    elif selected_tab == "Config File Generator":
        st.title("Config File Generator")
        if st.session_state.als_file and st.session_state.tumor_type:
            # Generate drug merge info from ALS file
            drug_merge_info = generate_drug_merge_info(st.session_state.als_file)
            if drug_merge_info is not None:
                # Save drug merge info to a temporary file
                temp_file = "drug_merge_info.csv"
                drug_merge_info.to_csv(temp_file, index=False)
                st.session_state.drug_merge_info = drug_merge_info
                st.success("Drug merge info generated successfully!")

            # Get user inputs
            col1, col2 = st.columns(2)
            with col1:
                blind = st.selectbox("Blinded data?", ["Blinded", "Unblinded"])
                lesion_metric = st.selectbox("Lesion Metric", ["sum", "max"])
            with col2:
                anchor_type = st.selectbox("Anchor Type", ["first_dose", "rand"])
                study_unit = st.selectbox("Study Unit for lesion", ["mm", "cm"])

            if st.button("Generate Config Files", key="generate_config"):
                with st.spinner("Generating config files..."):
                    # Save the uploaded file temporarily
                    with open("temp_als_file.xlsx", "wb") as f:
                        f.write(st.session_state.als_file.getvalue())

                    # Read the ALS file to get project name
                    als_project = pd.read_excel("temp_als_file.xlsx", sheet_name="CRFDraft", engine='openpyxl')

                    # Generate study name
                    study_name = re.sub(r'[^a-zA-Z0-9_]', '_', f"{blind[0].lower()}_{als_project['ProjectName'].iloc[0].lower().replace('-', '_')}")
                    st.session_state.study_name = study_name

                    # Clean up temporary file
                    Path("temp_als_file.xlsx").unlink()

                    output_path = generate_config_combo(
                        st.session_state.als_file,
                        blind[0].lower(),  # Get first letter (b/u)
                        lesion_metric,
                        anchor_type,
                        study_unit
                    )

                    # Read the generated file
                    with open(output_path, 'rb') as f:
                        file_data = f.read()

                    # Store config file data in session state for the download all feature
                    st.session_state.config_file_data = file_data

                    # Offer download
                    st.download_button(
                        label="Download Generated Config Files",
                        data=file_data,
                        file_name=output_path,
                        mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                    )

                    # Clean up
                    Path(output_path).unlink()

                    st.success("Config files generated successfully!")

                    # Automatically generate all R functions
                    with st.spinner("Generating all R functions..."):
                        if generate_all_functions():
                            st.success("✨ All R functions have been generated! Please review and confirm each function in the tabs below.")
                            st.info("💡 Navigate through the function tabs to review and confirm each generated function. Once confirmed, they will be available for download in the sidebar.")
                        else:
                            st.warning("Some functions could not be generated. Please check the individual function tabs for details.")
        else:
            st.info("Please complete the Cover Page setup first.")

    elif selected_tab == "Dose Merge Generator":
        st.title("Dose Merge Generator")
        if st.session_state.als_file and st.session_state.tumor_type and 'drug_merge_info' in st.session_state:
            # Add important note
            st.markdown("""
            <div style="background: linear-gradient(135deg, #fff3cd, #ffeaa7); padding: 1rem; border-radius: 12px; margin-bottom: 1.5rem; border-left: 4px solid #f39c12;">
                <div style="display: flex; align-items: center; margin-bottom: 0.5rem;">
                    <div style="font-size: 1.5rem; margin-right: 0.5rem;">⚠️</div>
                    <h4 style="color: #856404; margin: 0; font-weight: 600;">Important Note</h4>
                </div>
                <p style="color: #856404; margin: 0; font-size: 1rem; line-height: 1.4;">
                    <strong>Note:</strong> Please fill in the unit for study drug based on CRF.
                </p>
            </div>
            """, unsafe_allow_html=True)

            # Use the drug merge info directly from session state
            dose_merge_generator = DoseMergeGenerator(st.session_state.drug_merge_info)

            # Show configuration preview
            st.subheader("Configuration Preview")
            st.dataframe(dose_merge_generator.drug_info_df)

            # Display function confirmation UI
            display_function_confirmation_ui('dose_merge')
        else:
            st.info("Please complete the Cover Page setup and generate config files first.")

    elif selected_tab == "TD Merge Generator":
        st.title("TD Merge Generator")
        if st.session_state.als_file and st.session_state.tumor_type and 'study_name' in st.session_state:
            # Generate td merge info from ALS file
            td_merge_generator = TDMergeGenerator(st.session_state.als_file)

            # Show configuration preview
            st.subheader("Configuration Preview")
            st.dataframe(td_merge_generator.td_info_df)

            # Display function confirmation UI
            display_function_confirmation_ui('td_merge')
        else:
            st.info("Please complete the Cover Page setup and generate config files first.")

    elif selected_tab == "SubjectInfo Generator":
        st.title("SubjectInfo Generator")
        if st.session_state.als_file and st.session_state.tumor_type and 'study_name' in st.session_state:
            # Add important note
            st.markdown("""
            <div style="background: linear-gradient(135deg, #fff3cd, #ffeaa7); padding: 1rem; border-radius: 12px; margin-bottom: 1.5rem; border-left: 4px solid #f39c12;">
                <div style="display: flex; align-items: center; margin-bottom: 0.5rem;">
                    <div style="font-size: 1.5rem; margin-right: 0.5rem;">⚠️</div>
                    <h4 style="color: #856404; margin: 0; font-weight: 600;">Important Note</h4>
                </div>
                <p style="color: #856404; margin: 0; font-size: 1rem; line-height: 1.4;">
                    <strong>Note:</strong> Please check deriviation of onTreatment and MaxActualDose. If the treatment used is not the primary treatment, please update to the primary treatment.
                </p>
            </div>
            """, unsafe_allow_html=True)

            # Generate subject info from ALS file
            subject_info_generator = SubjectInfoGenerator(st.session_state.als_file, st.session_state.tumor_type)

            # Show configuration preview
            st.subheader("Configuration Preview")
            st.dataframe(subject_info_generator.subject_info_df)

            # Display function confirmation UI
            display_function_confirmation_ui('subject_info')
        else:
            st.info("Please complete the Cover Page setup and generate config files first.")

    elif selected_tab == "PatientProfile Generator":
        st.title("PatientProfile Generator")
        if st.session_state.als_file and st.session_state.tumor_type and 'study_name' in st.session_state:
            # Generate patient profile from ALS file with tumor type
            patient_profile_generator = PatientProfileGenerator(st.session_state.als_file, st.session_state.tumor_type)

            # Show configuration preview
            st.subheader("Configuration Preview")
            st.dataframe(patient_profile_generator.patient_profile_df)

            # Display function confirmation UI
            display_function_confirmation_ui('patient_profile')
        else:
            st.info("Please complete the Cover Page setup and generate config files first.")

    elif selected_tab == "AEWriteOut Generator":
        st.title("AEWriteOut Generator")
        if st.session_state.als_file and st.session_state.tumor_type and 'study_name' in st.session_state:
            # Generate AE WriteOut from ALS file with tumor type
            ae_writeout_generator = AEWriteOutGenerator(st.session_state.als_file, st.session_state.tumor_type)

            # Show configuration preview
            st.subheader("Configuration Preview")
            st.dataframe(ae_writeout_generator.ae_writeout_df)

            # Display function confirmation UI
            display_function_confirmation_ui('ae_writeout')
        else:
            st.info("Please complete the Cover Page setup and generate config files first.")

    elif selected_tab == "AE_CMlink Generator":
        st.title("AE_CMlink Function Generator")
        if st.session_state.als_file and st.session_state.tumor_type and 'study_name' in st.session_state:
            try:
                # Load config file first
                config_df = load_config_file()
                st.success("Configuration file loaded successfully")

                # Show config preview
                with st.expander("View Configuration"):
                    st.dataframe(config_df)

                # Find matching variables using the stored var_label_value
                if 'var_label_value' in st.session_state:
                    variable_mappings, replacements_info = find_matching_variables(st.session_state.var_label_value, config_df)

                    if not variable_mappings:
                        st.warning("No matching variables found based on the patterns in the config file.")
                    else:
                        # Show matched variables
                        st.write("Matched Variables:")
                        matched_df = pd.DataFrame(replacements_info)
                        st.dataframe(matched_df)

                        # Display function confirmation UI
                        display_function_confirmation_ui('ae_cm_link')
                else:
                    st.warning("Variable mapping data not found. Please regenerate config files.")

            except Exception as e:
                st.error(f"Failed to load configuration file: {str(e)}")
        else:
            st.info("Please complete the Cover Page setup and generate config files first.")

    elif selected_tab == "SAE Flag Generator":
        st.title("SAE Flag Function Generator")
        if st.session_state.als_file and st.session_state.tumor_type and 'study_name' in st.session_state:
            try:
                # Load config file first
                config_df = load_config_file()
                st.success("Configuration file loaded successfully")

                # Show config preview
                with st.expander("View Configuration"):
                    st.dataframe(config_df)

                # Find matching variables using the stored var_label_value
                if 'var_label_value' in st.session_state:
                    variable_mappings, replacements_info = find_matching_variables(st.session_state.var_label_value, config_df)

                    if not variable_mappings:
                        st.warning("No matching variables found based on the patterns in the config file.")
                    else:
                        # Show matched variables
                        st.write("Matched Variables:")
                        matched_df = pd.DataFrame(replacements_info)
                        st.dataframe(matched_df)

                        # Display function confirmation UI
                        display_function_confirmation_ui('sae_flag')
                else:
                    st.warning("Variable mapping data not found. Please regenerate config files.")

            except Exception as e:
                st.error(f"Failed to load configuration file: {str(e)}")
        else:
            st.info("Please complete the Cover Page setup and generate config files first.")

    elif selected_tab == "MH Flag Generator":
        st.title("MH Flag Function Generator")
        if st.session_state.als_file and st.session_state.tumor_type and 'study_name' in st.session_state:
            try:
                # Load config file first
                config_df = load_config_file()
                st.success("Configuration file loaded successfully")

                # Show config preview
                with st.expander("View Configuration"):
                    st.dataframe(config_df)

                # Find matching variables using the stored var_label_value
                if 'var_label_value' in st.session_state:
                    variable_mappings, replacements_info = find_matching_variables(st.session_state.var_label_value, config_df)

                    if not variable_mappings:
                        st.warning("No matching variables found based on the patterns in the config file.")
                    else:
                        # Show matched variables
                        st.write("Matched Variables:")
                        matched_df = pd.DataFrame(replacements_info)
                        st.dataframe(matched_df)

                        # Display function confirmation UI
                        display_function_confirmation_ui('mh_flag')
                else:
                    st.warning("Variable mapping data not found. Please regenerate config files.")

            except Exception as e:
                st.error(f"Failed to load configuration file: {str(e)}")
        else:
            st.info("Please complete the Cover Page setup and generate config files first.")

    elif selected_tab == "Vital CTCAE Generator":
        st.title("Vital CTCAE Function Generator")
        if st.session_state.als_file and st.session_state.tumor_type and 'study_name' in st.session_state:
            try:
                # Load config file first
                config_df = load_config_file()
                st.success("Configuration file loaded successfully")

                # Show config preview
                with st.expander("View Configuration"):
                    st.dataframe(config_df)

                # Find matching variables using the stored var_label_value
                if 'var_label_value' in st.session_state:
                    variable_mappings, replacements_info = find_matching_variables(st.session_state.var_label_value, config_df)

                    if not variable_mappings:
                        st.warning("No matching variables found based on the patterns in the config file.")
                    else:
                        # Show matched variables
                        st.write("Matched Variables:")
                        matched_df = pd.DataFrame(replacements_info)
                        st.dataframe(matched_df)

                        # Display function confirmation UI
                        display_function_confirmation_ui('vital_ctcae')
                else:
                    st.warning("Variable mapping data not found. Please regenerate config files.")

            except Exception as e:
                st.error(f"Failed to load configuration file: {str(e)}")
        else:
            st.info("Please complete the Cover Page setup and generate config files first.")

    elif selected_tab == "aeTox Flag Generator":
        st.title("aeTox Flag Function Generator")
        if st.session_state.als_file and st.session_state.tumor_type and 'study_name' in st.session_state:
            try:
                # Load config file first
                config_df = load_config_file()
                st.success("Configuration file loaded successfully")

                # Show config preview
                with st.expander("View Configuration"):
                    st.dataframe(config_df)

                # Find matching variables using the stored var_label_value
                if 'var_label_value' in st.session_state:
                    variable_mappings, replacements_info = find_matching_variables(st.session_state.var_label_value, config_df)

                    if not variable_mappings:
                        st.warning("No matching variables found based on the patterns in the config file.")
                    else:
                        # Show matched variables
                        st.write("Matched Variables:")
                        matched_df = pd.DataFrame(replacements_info)
                        st.dataframe(matched_df)

                        # Display function confirmation UI
                        display_function_confirmation_ui('aetox_flag')
                else:
                    st.warning("Variable mapping data not found. Please regenerate config files.")

            except Exception as e:
                st.error(f"Failed to load configuration file: {str(e)}")
        else:
            st.info("Please complete the Cover Page setup and generate config files first.")

    elif selected_tab == "labCalc Generator":
        st.title("labCalc Function Generator")
        if st.session_state.als_file and st.session_state.tumor_type and 'study_name' in st.session_state:
            try:
                # Load config file first
                config_df = load_config_file()
                st.success("Configuration file loaded successfully")

                # Show config preview
                with st.expander("View Configuration"):
                    st.dataframe(config_df)

                # Find matching variables using the stored var_label_value
                if 'var_label_value' in st.session_state:
                    variable_mappings, replacements_info = find_matching_variables(st.session_state.var_label_value, config_df)

                    if not variable_mappings:
                        st.warning("No matching variables found based on the patterns in the config file.")
                    else:
                        # Show matched variables
                        st.write("Matched Variables:")
                        matched_df = pd.DataFrame(replacements_info)
                        st.dataframe(matched_df)

                        # Display function confirmation UI
                        display_function_confirmation_ui('labcalc')
                else:
                    st.warning("Variable mapping data not found. Please regenerate config files.")

            except Exception as e:
                st.error(f"Failed to load configuration file: {str(e)}")
        else:
            st.info("Please complete the Cover Page setup and generate config files first.")

    elif selected_tab == "Controller Generator":
        st.title("Controller Generator")
        if st.session_state.als_file and st.session_state.tumor_type and 'study_name' in st.session_state:
            # Add important note
            st.markdown("""
            <div style="background: linear-gradient(135deg, #fff3cd, #ffeaa7); padding: 1rem; border-radius: 12px; margin-bottom: 1.5rem; border-left: 4px solid #f39c12;">
                <div style="display: flex; align-items: center; margin-bottom: 0.5rem;">
                    <div style="font-size: 1.5rem; margin-right: 0.5rem;">⚠️</div>
                    <h4 style="color: #856404; margin: 0; font-weight: 600;">Important Note</h4>
                </div>
                <p style="color: #856404; margin: 0; font-size: 1rem; line-height: 1.4;">
                    <strong>Note:</strong> 1. Please check if the paths in Controller are correct;<br>
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;2. Please check parameters in labCTCAE function, we use default values in this tool;<br>
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;3. Please check units in eg_stand and vs_merged, we use default values in this tool;<br>
                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;4. Please adjust variables in appendCol based on the real situation, we give default choices in this tool.
                </p>
            </div>
            """, unsafe_allow_html=True)

            # Display function confirmation UI
            display_function_confirmation_ui('controller')
        else:
            st.info("Please complete the Cover Page setup and generate config files first.")

if __name__ == "__main__":
    main()